/* Custom CSS for Government HR System */

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.2rem;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
    border-radius: 8px;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
    border-radius: 8px;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    border-radius: 8px;
}

.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f1f3f4;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.stats-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-left: 4px solid #007bff;
    padding: 20px;
    margin-bottom: 20px;
}

.stats-card.success {
    border-left-color: #28a745;
}

.stats-card.warning {
    border-left-color: #ffc107;
}

.stats-card.danger {
    border-left-color: #dc3545;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #007bff;
}

.stats-label {
    font-size: 1.1rem;
    color: #6c757d;
    font-weight: 500;
}

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h2 {
    color: #333;
    font-weight: 700;
    margin-bottom: 10px;
}

.login-header p {
    color: #6c757d;
}

.alert {
    border-radius: 8px;
    border: none;
}

.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
}

.page-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: 10px;
}

.page-header h1 {
    margin: 0;
    font-weight: 600;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .login-card {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
}

/* Print styles */
@media print {
    .navbar, .btn, .page-header {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
