{% extends "base.html" %}

{% block title %}إضافة موظف جديد - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-user-plus"></i> إضافة موظف جديد</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('employees') }}">الموظفين</a></li>
                        <li class="breadcrumb-item active">إضافة موظف</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus"></i>
                        بيانات الموظف الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="employeeForm">
                        <div class="row">
                            <!-- المعلومات الشخصية -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user"></i> المعلومات الشخصية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="employee_number" class="form-label">رقم الموظف *</label>
                                <input type="text" class="form-control" id="employee_number" name="employee_number" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="national_id" class="form-label">رقم الهوية الوطنية *</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="birth_date" class="form-label">تاريخ الميلاد *</label>
                                <input type="date" class="form-control past-date" id="birth_date" name="birth_date" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">الجنس</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male">ذكر</option>
                                    <option value="female">أنثى</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                <select class="form-select" id="marital_status" name="marital_status">
                                    <option value="">اختر الحالة</option>
                                    <option value="single">أعزب</option>
                                    <option value="married">متزوج</option>
                                    <option value="divorced">مطلق</option>
                                    <option value="widowed">أرمل</option>
                                </select>
                            </div>
                            
                            <!-- معلومات الاتصال -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-phone"></i> معلومات الاتصال
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                            </div>
                            
                            <!-- المعلومات الوظيفية -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-briefcase"></i> المعلومات الوظيفية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="department_id" class="form-label">القسم *</label>
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <option value="">اختر القسم</option>
                                    {% for department in departments %}
                                    <option value="{{ department.id }}">{{ department.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="job_grade_id" class="form-label">الدرجة الوظيفية *</label>
                                <select class="form-select" id="job_grade_id" name="job_grade_id" required>
                                    <option value="">اختر الدرجة</option>
                                    {% for grade in job_grades %}
                                    <option value="{{ grade.id }}" data-salary="{{ grade.basic_salary }}">
                                        {{ grade.grade_name }} ({{ "{:,.0f}".format(grade.basic_salary) }} د.ع)
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="job_title" class="form-label">المنصب الوظيفي *</label>
                                <input type="text" class="form-control" id="job_title" name="job_title" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="current_salary" class="form-label">الراتب الحالي (د.ع) *</label>
                                <input type="number" class="form-control" id="current_salary" name="current_salary" step="1000" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="hire_date" class="form-label">تاريخ التوظيف *</label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date" required>
                            </div>
                            
                            <!-- المعلومات التعليمية -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-graduation-cap"></i> المعلومات التعليمية
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="education_level" class="form-label">المستوى التعليمي</label>
                                <select class="form-select" id="education_level" name="education_level">
                                    <option value="">اختر المستوى</option>
                                    <option value="primary">ابتدائية</option>
                                    <option value="secondary">متوسطة</option>
                                    <option value="high_school">إعدادية</option>
                                    <option value="diploma">دبلوم</option>
                                    <option value="bachelor">بكالوريوس</option>
                                    <option value="master">ماجستير</option>
                                    <option value="phd">دكتوراه</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="specialization" class="form-label">التخصص</label>
                                <input type="text" class="form-control" id="specialization" name="specialization">
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> حفظ الموظف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-fill salary when job grade is selected
    document.getElementById('job_grade_id').addEventListener('change', function() {
        var selectedOption = this.options[this.selectedIndex];
        var salary = selectedOption.getAttribute('data-salary');
        if (salary) {
            document.getElementById('current_salary').value = salary;
        }
    });
    
    // Generate employee number automatically
    generateEmployeeNumber();
});

function generateEmployeeNumber() {
    var now = new Date();
    var year = now.getFullYear();
    var month = String(now.getMonth() + 1).padStart(2, '0');
    var random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    var employeeNumber = year + month + random;
    document.getElementById('employee_number').value = employeeNumber;
}
</script>
{% endblock %}
