{% extends "base.html" %}

{% block title %}أنواع العلاوات - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-list"></i> أنواع العلاوات</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('allowances') }}">العلاوات</a></li>
                        <li class="breadcrumb-item active">أنواع العلاوات</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('add_allowance_type') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> إضافة نوع جديد
                </a>
                <a href="{{ url_for('allowances') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للعلاوات
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list"></i>
                قائمة أنواع العلاوات
            </h5>
        </div>
        <div class="card-body">
            {% if allowance_types %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم النوع</th>
                                <th>الرمز</th>
                                <th>نوع المبلغ</th>
                                <th>المبلغ الافتراضي</th>
                                <th>الوصف</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for allowance_type in allowance_types %}
                            <tr>
                                <td>{{ allowance_type.name }}</td>
                                <td><code>{{ allowance_type.code }}</code></td>
                                <td>
                                    {% if allowance_type.amount_type == 'fixed' %}
                                        <span class="badge bg-primary">مبلغ ثابت</span>
                                    {% else %}
                                        <span class="badge bg-info">نسبة مئوية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if allowance_type.default_amount %}
                                        {% if allowance_type.amount_type == 'fixed' %}
                                            {{ "{:,.0f}".format(allowance_type.default_amount) }} د.ع
                                        {% else %}
                                            {{ allowance_type.default_amount }}%
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ allowance_type.description or '-' }}</td>
                                <td>
                                    {% if allowance_type.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-warning" onclick="editAllowanceType({{ allowance_type.id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-{{ 'secondary' if allowance_type.is_active else 'success' }}" 
                                                onclick="toggleAllowanceType({{ allowance_type.id }})" 
                                                title="{{ 'إلغاء تفعيل' if allowance_type.is_active else 'تفعيل' }}">
                                            <i class="fas fa-{{ 'pause' if allowance_type.is_active else 'play' }}"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteAllowanceType({{ allowance_type.id }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-list fa-3x mb-3"></i>
                    <h5>لا توجد أنواع علاوات</h5>
                    <p>لم يتم إضافة أي أنواع علاوات بعد</p>
                    <a href="{{ url_for('add_allowance_type') }}" class="btn btn-primary">إضافة أول نوع علاوة</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal لتعديل نوع العلاوة -->
<div class="modal fade" id="editAllowanceTypeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل نوع العلاوة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editAllowanceTypeForm">
                    <input type="hidden" id="edit_allowance_type_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم النوع *</label>
                        <input type="text" class="form-control" id="edit_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_code" class="form-label">الرمز *</label>
                        <input type="text" class="form-control" id="edit_code" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_amount_type" class="form-label">نوع المبلغ *</label>
                        <select class="form-select" id="edit_amount_type" required>
                            <option value="fixed">مبلغ ثابت</option>
                            <option value="percentage">نسبة مئوية</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_default_amount" class="form-label">المبلغ الافتراضي</label>
                        <input type="number" class="form-control" id="edit_default_amount" step="0.1">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveAllowanceType()">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function editAllowanceType(typeId) {
    // جلب بيانات نوع العلاوة وعرضها في المودال
    fetch(`/allowance-types/${typeId}/data`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('edit_allowance_type_id').value = typeId;
                document.getElementById('edit_name').value = data.data.name;
                document.getElementById('edit_code').value = data.data.code;
                document.getElementById('edit_amount_type').value = data.data.amount_type;
                document.getElementById('edit_default_amount').value = data.data.default_amount || '';
                document.getElementById('edit_description').value = data.data.description || '';
                
                var modal = new bootstrap.Modal(document.getElementById('editAllowanceTypeModal'));
                modal.show();
            } else {
                alert('حدث خطأ في جلب البيانات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
}

function saveAllowanceType() {
    var typeId = document.getElementById('edit_allowance_type_id').value;
    var formData = new FormData();
    
    formData.append('name', document.getElementById('edit_name').value);
    formData.append('code', document.getElementById('edit_code').value);
    formData.append('amount_type', document.getElementById('edit_amount_type').value);
    formData.append('default_amount', document.getElementById('edit_default_amount').value);
    formData.append('description', document.getElementById('edit_description').value);
    
    fetch(`/allowance-types/${typeId}/edit`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ أثناء الحفظ');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function toggleAllowanceType(typeId) {
    fetch(`/allowance-types/${typeId}/toggle`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ أثناء التحديث');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function deleteAllowanceType(typeId) {
    if (confirm('هل أنت متأكد من حذف هذا النوع؟ سيتم حذف جميع العلاوات المرتبطة به.')) {
        fetch(`/allowance-types/${typeId}/delete`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
