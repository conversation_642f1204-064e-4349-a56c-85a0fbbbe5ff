{% extends "base.html" %}

{% block title %}قائمة الموظفين - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-users"></i> قائمة الموظفين</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الموظفين</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('add_employee') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> إضافة موظف جديد
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- شريط البحث -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="البحث عن موظف..." value="{{ search }}">
                <button type="submit" class="btn btn-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
                {% if search %}
                <a href="{{ url_for('employees') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-times"></i>
                </a>
                {% endif %}
            </form>
        </div>
        <div class="col-md-6 text-end">
            <button class="btn btn-outline-success" onclick="exportToExcel('employeesTable')">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </button>
            <button class="btn btn-outline-primary" onclick="printPage()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <!-- جدول الموظفين -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list"></i>
                قائمة الموظفين
                {% if search %}
                <small class="text-muted">(نتائج البحث عن: "{{ search }}")</small>
                {% endif %}
            </h5>
        </div>
        <div class="card-body">
            {% if employees.items %}
                <div class="table-responsive">
                    <table class="table table-hover" id="employeesTable">
                        <thead>
                            <tr>
                                <th>رقم الموظف</th>
                                <th>الاسم الكامل</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>الدرجة الوظيفية</th>
                                <th>الراتب الحالي</th>
                                <th>تاريخ التوظيف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees.items %}
                            <tr>
                                <td>{{ employee.employee_number }}</td>
                                <td>
                                    <a href="{{ url_for('employee_detail', id=employee.id) }}" class="text-decoration-none">
                                        {{ employee.full_name }}
                                    </a>
                                </td>
                                <td>{{ employee.department.name }}</td>
                                <td>{{ employee.job_title }}</td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{ employee.job_grade.grade_name }}
                                    </span>
                                </td>
                                <td class="format-number">{{ "{:,.0f}".format(employee.current_salary) }} د.ع</td>
                                <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('employee_detail', id=employee.id) }}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_employee', id=employee.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-danger btn-delete" 
                                                onclick="deleteEmployee({{ employee.id }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if employees.pages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if employees.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees', page=employees.prev_num, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in employees.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != employees.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employees', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if employees.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees', page=employees.next_num, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <h5>لا توجد موظفين</h5>
                    {% if search %}
                        <p>لم يتم العثور على نتائج للبحث "{{ search }}"</p>
                        <a href="{{ url_for('employees') }}" class="btn btn-secondary">عرض جميع الموظفين</a>
                    {% else %}
                        <p>لم يتم إضافة أي موظفين بعد</p>
                        <a href="{{ url_for('add_employee') }}" class="btn btn-primary">إضافة أول موظف</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function deleteEmployee(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟ سيتم حذف جميع البيانات المرتبطة به.')) {
        fetch(`/employees/${employeeId}/delete`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
