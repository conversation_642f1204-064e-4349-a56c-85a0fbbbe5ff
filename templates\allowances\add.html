{% extends "base.html" %}

{% block title %}إضافة علاوة جديدة - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-plus-circle"></i> إضافة علاوة جديدة</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('allowances') }}">العلاوات</a></li>
                        <li class="breadcrumb-item active">إضافة علاوة</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('allowances') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle"></i>
                        بيانات العلاوة الجديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="allowanceForm">
                        <div class="row">
                            <!-- معلومات الموظف -->
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user"></i> معلومات الموظف
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">الموظف *</label>
                                <select class="form-select" id="employee_id" name="employee_id" required>
                                    <option value="">اختر الموظف</option>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}" data-salary="{{ employee.current_salary }}">
                                        {{ employee.full_name }} - {{ employee.employee_number }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="current_salary_display" class="form-label">الراتب الحالي</label>
                                <input type="text" class="form-control" id="current_salary_display" readonly>
                            </div>
                            
                            <!-- معلومات العلاوة -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-money-bill-wave"></i> معلومات العلاوة
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="allowance_type_id" class="form-label">نوع العلاوة *</label>
                                <select class="form-select" id="allowance_type_id" name="allowance_type_id" required>
                                    <option value="">اختر نوع العلاوة</option>
                                    {% for allowance_type in allowance_types %}
                                    <option value="{{ allowance_type.id }}" 
                                            data-amount-type="{{ allowance_type.amount_type }}"
                                            data-default-amount="{{ allowance_type.default_amount or '' }}">
                                        {{ allowance_type.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">مبلغ العلاوة (د.ع) *</label>
                                <input type="number" class="form-control" id="amount" name="amount" step="1000" required>
                                <div class="form-text" id="amount_help"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="date_granted" class="form-label">تاريخ المنح *</label>
                                <input type="date" class="form-control" id="date_granted" name="date_granted" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="effective_date" class="form-label">تاريخ السريان *</label>
                                <input type="date" class="form-control" id="effective_date" name="effective_date" required>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="reason" class="form-label">سبب العلاوة</label>
                                <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="اذكر سبب منح العلاوة..."></textarea>
                            </div>
                            
                            <!-- معلومات الاعتماد -->
                            <div class="col-12 mt-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-check-circle"></i> معلومات الاعتماد
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">حالة العلاوة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="pending">معلقة</option>
                                    <option value="approved">معتمدة</option>
                                    <option value="rejected">مرفوضة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="approved_by" class="form-label">المعتمد من</label>
                                <input type="text" class="form-control" id="approved_by" name="approved_by" placeholder="اسم المعتمد">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('allowances') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> حفظ العلاوة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // عرض الراتب الحالي عند اختيار الموظف
    document.getElementById('employee_id').addEventListener('change', function() {
        var selectedOption = this.options[this.selectedIndex];
        var salary = selectedOption.getAttribute('data-salary');
        var salaryDisplay = document.getElementById('current_salary_display');
        
        if (salary) {
            salaryDisplay.value = parseFloat(salary).toLocaleString('ar-IQ') + ' د.ع';
        } else {
            salaryDisplay.value = '';
        }
    });
    
    // تحديث مبلغ العلاوة عند اختيار نوع العلاوة
    document.getElementById('allowance_type_id').addEventListener('change', function() {
        var selectedOption = this.options[this.selectedIndex];
        var amountType = selectedOption.getAttribute('data-amount-type');
        var defaultAmount = selectedOption.getAttribute('data-default-amount');
        var amountInput = document.getElementById('amount');
        var amountHelp = document.getElementById('amount_help');
        
        if (amountType === 'percentage') {
            amountHelp.textContent = 'أدخل النسبة المئوية (مثال: 10 للحصول على 10%)';
            amountInput.placeholder = 'النسبة المئوية';
            amountInput.step = '0.1';
        } else {
            amountHelp.textContent = 'أدخل المبلغ الثابت بالدينار العراقي';
            amountInput.placeholder = 'المبلغ بالدينار';
            amountInput.step = '1000';
        }
        
        if (defaultAmount) {
            amountInput.value = defaultAmount;
        }
    });
    
    // تعيين التاريخ الحالي كافتراضي
    var today = new Date().toISOString().split('T')[0];
    document.getElementById('date_granted').value = today;
    document.getElementById('effective_date').value = today;
});
</script>
{% endblock %}
