{% extends "base.html" %}

{% block title %}قائمة العلاوات - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-money-bill-wave"></i> قائمة العلاوات</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">العلاوات</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('add_allowance') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> إضافة علاوة جديدة
                </a>
                <a href="{{ url_for('allowance_types') }}" class="btn btn-info">
                    <i class="fas fa-list"></i> أنواع العلاوات
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- شريط البحث -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" class="d-flex">
                <input type="text" class="form-control" name="search" placeholder="البحث عن موظف..." value="{{ search }}">
                <button type="submit" class="btn btn-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
                {% if search %}
                <a href="{{ url_for('allowances') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-times"></i>
                </a>
                {% endif %}
            </form>
        </div>
        <div class="col-md-6 text-end">
            <button class="btn btn-outline-success" onclick="exportToExcel('allowancesTable')">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </button>
            <button class="btn btn-outline-primary" onclick="printPage()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <!-- جدول العلاوات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list"></i>
                قائمة العلاوات
                {% if search %}
                <small class="text-muted">(نتائج البحث عن: "{{ search }}")</small>
                {% endif %}
            </h5>
        </div>
        <div class="card-body">
            {% if allowances.items %}
                <div class="table-responsive">
                    <table class="table table-hover" id="allowancesTable">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>نوع العلاوة</th>
                                <th>المبلغ</th>
                                <th>تاريخ المنح</th>
                                <th>تاريخ السريان</th>
                                <th>الحالة</th>
                                <th>المعتمد من</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for allowance in allowances.items %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('employee_detail', id=allowance.employee.id) }}" class="text-decoration-none">
                                        {{ allowance.employee.full_name }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ allowance.employee.employee_number }}</small>
                                </td>
                                <td>{{ allowance.allowance_type.name }}</td>
                                <td class="format-number">{{ "{:,.0f}".format(allowance.amount) }} د.ع</td>
                                <td>{{ allowance.date_granted.strftime('%Y-%m-%d') }}</td>
                                <td>{{ allowance.effective_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if allowance.status == 'approved' %}
                                        <span class="badge bg-success">معتمدة</span>
                                    {% elif allowance.status == 'pending' %}
                                        <span class="badge bg-warning">معلقة</span>
                                    {% elif allowance.status == 'rejected' %}
                                        <span class="badge bg-danger">مرفوضة</span>
                                    {% endif %}
                                </td>
                                <td>{{ allowance.approved_by or '-' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="viewAllowance({{ allowance.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if allowance.status == 'pending' %}
                                        <button class="btn btn-outline-success" onclick="approveAllowance({{ allowance.id }})" title="اعتماد">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="rejectAllowance({{ allowance.id }})" title="رفض">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-outline-warning" onclick="editAllowance({{ allowance.id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if allowances.pages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if allowances.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('allowances', page=allowances.prev_num, search=search) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in allowances.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != allowances.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('allowances', page=page_num, search=search) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if allowances.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('allowances', page=allowances.next_num, search=search) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-money-bill-wave fa-3x mb-3"></i>
                    <h5>لا توجد علاوات</h5>
                    {% if search %}
                        <p>لم يتم العثور على نتائج للبحث "{{ search }}"</p>
                        <a href="{{ url_for('allowances') }}" class="btn btn-secondary">عرض جميع العلاوات</a>
                    {% else %}
                        <p>لم يتم إضافة أي علاوات بعد</p>
                        <a href="{{ url_for('add_allowance') }}" class="btn btn-primary">إضافة أول علاوة</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function viewAllowance(allowanceId) {
    // عرض تفاصيل العلاوة في modal
    alert('سيتم تطوير عرض التفاصيل قريباً');
}

function approveAllowance(allowanceId) {
    if (confirm('هل أنت متأكد من اعتماد هذه العلاوة؟')) {
        fetch(`/allowances/${allowanceId}/approve`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الاعتماد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function rejectAllowance(allowanceId) {
    if (confirm('هل أنت متأكد من رفض هذه العلاوة؟')) {
        fetch(`/allowances/${allowanceId}/reject`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الرفض');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function editAllowance(allowanceId) {
    window.location.href = `/allowances/${allowanceId}/edit`;
}
</script>
{% endblock %}
