{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item active">الرئيسية</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <span class="badge bg-light text-dark">{{ current_user.full_name }}</span>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <div class="stats-number">{{ total_employees }}</div>
                    <div class="stats-label">إجمالي الموظفين</div>
                    <i class="fas fa-users fa-2x text-primary mt-2"></i>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card stats-card success">
                <div class="card-body text-center">
                    <div class="stats-number text-success">{{ total_allowances }}</div>
                    <div class="stats-label">إجمالي العلاوات</div>
                    <i class="fas fa-money-bill-wave fa-2x text-success mt-2"></i>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card stats-card warning">
                <div class="card-body text-center">
                    <div class="stats-number text-warning">{{ total_promotions }}</div>
                    <div class="stats-label">إجمالي الترفيعات</div>
                    <i class="fas fa-arrow-up fa-2x text-warning mt-2"></i>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card stats-card danger">
                <div class="card-body text-center">
                    <div class="stats-number text-danger">5</div>
                    <div class="stats-label">المعاملات المعلقة</div>
                    <i class="fas fa-clock fa-2x text-danger mt-2"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- العلاوات الحديثة -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave"></i>
                        العلاوات الحديثة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_allowances %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>نوع العلاوة</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for allowance in recent_allowances %}
                                    <tr>
                                        <td>{{ allowance.employee.full_name }}</td>
                                        <td>{{ allowance.allowance_type.name }}</td>
                                        <td>{{ "{:,.0f}".format(allowance.amount) }} د.ع</td>
                                        <td>{{ allowance.date_granted.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('allowances') }}" class="btn btn-primary btn-sm">
                                عرض جميع العلاوات
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>لا توجد علاوات حديثة</p>
                            <a href="{{ url_for('add_allowance') }}" class="btn btn-primary">
                                إضافة علاوة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الترفيعات الحديثة -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-arrow-up"></i>
                        الترفيعات الحديثة
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_promotions %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>من درجة</th>
                                        <th>إلى درجة</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for promotion in recent_promotions %}
                                    <tr>
                                        <td>{{ promotion.employee.full_name }}</td>
                                        <td>{{ promotion.from_grade.grade_number }}</td>
                                        <td>{{ promotion.to_grade.grade_number }}</td>
                                        <td>{{ promotion.promotion_date.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('promotions') }}" class="btn btn-primary btn-sm">
                                عرض جميع الترفيعات
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>لا توجد ترفيعات حديثة</p>
                            <a href="{{ url_for('add_promotion') }}" class="btn btn-primary">
                                إضافة ترفيع جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('add_employee') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                                إضافة موظف جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('add_allowance') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                                إضافة علاوة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('add_promotion') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-arrow-up fa-2x d-block mb-2"></i>
                                إضافة ترفيع
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                                التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
