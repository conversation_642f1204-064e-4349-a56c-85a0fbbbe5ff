{% extends "base.html" %}

{% block title %}إضافة نوع علاوة جديد - نظام إدارة العلاوات والترفيعات{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-plus"></i> إضافة نوع علاوة جديد</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('allowances') }}">العلاوات</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('allowance_types') }}">أنواع العلاوات</a></li>
                        <li class="breadcrumb-item active">إضافة نوع جديد</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('allowance_types') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus"></i>
                        بيانات نوع العلاوة الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="allowanceTypeForm">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم نوع العلاوة *</label>
                            <input type="text" class="form-control" id="name" name="name" required placeholder="مثال: علاوة سنوية">
                        </div>
                        
                        <div class="mb-3">
                            <label for="code" class="form-label">رمز النوع *</label>
                            <input type="text" class="form-control" id="code" name="code" required placeholder="مثال: ANNUAL" style="text-transform: uppercase;">
                            <div class="form-text">يجب أن يكون الرمز فريداً ويحتوي على أحرف إنجليزية فقط</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="amount_type" class="form-label">نوع المبلغ *</label>
                            <select class="form-select" id="amount_type" name="amount_type" required>
                                <option value="">اختر نوع المبلغ</option>
                                <option value="fixed">مبلغ ثابت (دينار عراقي)</option>
                                <option value="percentage">نسبة مئوية من الراتب</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="default_amount" class="form-label">المبلغ الافتراضي</label>
                            <input type="number" class="form-control" id="default_amount" name="default_amount" step="0.1" placeholder="اختياري">
                            <div class="form-text" id="amount_help">سيتم استخدام هذا المبلغ كقيمة افتراضية عند إضافة علاوة من هذا النوع</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف مختصر لنوع العلاوة..."></textarea>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('allowance_types') }}" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> حفظ نوع العلاوة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- بطاقة معلومات -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        معلومات مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">المبلغ الثابت</h6>
                            <p class="small text-muted">
                                يتم إضافة مبلغ محدد بالدينار العراقي إلى راتب الموظف
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">النسبة المئوية</h6>
                            <p class="small text-muted">
                                يتم حساب العلاوة كنسبة مئوية من الراتب الحالي للموظف
                            </p>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb"></i>
                        <strong>نصيحة:</strong> استخدم أرقاماً واضحة للرموز مثل ANNUAL, PROMOTION, EXCEPTIONAL لسهولة التمييز
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث نص المساعدة حسب نوع المبلغ
    document.getElementById('amount_type').addEventListener('change', function() {
        var amountHelp = document.getElementById('amount_help');
        var defaultAmountInput = document.getElementById('default_amount');
        
        if (this.value === 'percentage') {
            amountHelp.textContent = 'أدخل النسبة المئوية (مثال: 5 للحصول على 5% من الراتب)';
            defaultAmountInput.placeholder = 'النسبة المئوية (مثال: 5)';
            defaultAmountInput.step = '0.1';
            defaultAmountInput.max = '100';
        } else if (this.value === 'fixed') {
            amountHelp.textContent = 'أدخل المبلغ الثابت بالدينار العراقي';
            defaultAmountInput.placeholder = 'المبلغ بالدينار (مثال: 50000)';
            defaultAmountInput.step = '1000';
            defaultAmountInput.removeAttribute('max');
        } else {
            amountHelp.textContent = 'سيتم استخدام هذا المبلغ كقيمة افتراضية عند إضافة علاوة من هذا النوع';
            defaultAmountInput.placeholder = 'اختياري';
        }
    });
    
    // تحويل الرمز إلى أحرف كبيرة
    document.getElementById('code').addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z_]/g, '');
    });
    
    // توليد رمز تلقائي من الاسم
    document.getElementById('name').addEventListener('input', function() {
        var name = this.value;
        var code = '';
        
        // استخراج الكلمات العربية وتحويلها لرموز إنجليزية
        if (name.includes('سنوية')) code += 'ANNUAL_';
        if (name.includes('استثنائية')) code += 'EXCEPTIONAL_';
        if (name.includes('ترقية')) code += 'PROMOTION_';
        if (name.includes('تقدير')) code += 'APPRECIATION_';
        if (name.includes('خدمة')) code += 'SERVICE_';
        if (name.includes('أداء')) code += 'PERFORMANCE_';
        if (name.includes('مكافأة')) code += 'BONUS_';
        
        // إزالة الشرطة السفلية الأخيرة
        code = code.replace(/_$/, '');
        
        // إذا لم يتم العثور على كلمات معروفة، استخدم الأحرف الأولى
        if (!code) {
            var words = name.split(' ');
            code = words.map(word => word.charAt(0)).join('').toUpperCase();
        }
        
        document.getElementById('code').value = code;
    });
});
</script>
{% endblock %}
