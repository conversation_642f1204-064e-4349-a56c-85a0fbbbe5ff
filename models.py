from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date

def create_models(db):
    """إنشاء جميع النماذج"""
    
    class User(UserMixin, db.Model):
        """نموذج المستخدمين"""
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        password_hash = db.Column(db.String(255), nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(50), nullable=False, default='user')  # admin, hr_manager, user
        is_active = db.Column(db.<PERSON>, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        def set_password(self, password):
            self.password_hash = generate_password_hash(password)
        
        def check_password(self, password):
            return check_password_hash(self.password_hash, password)

    class Department(db.Model):
        """نموذج الأقسام"""
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        code = db.Column(db.String(50), unique=True, nullable=False)
        description = db.Column(db.Text)
        manager_id = db.Column(db.Integer, db.ForeignKey('employee.id'))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)

        # العلاقات
        employees = db.relationship('Employee', foreign_keys='Employee.department_id', backref='department', lazy=True)

    class JobGrade(db.Model):
        """نموذج الدرجات الوظيفية"""
        id = db.Column(db.Integer, primary_key=True)
        grade_number = db.Column(db.Integer, nullable=False, unique=True)
        grade_name = db.Column(db.String(100), nullable=False)
        basic_salary = db.Column(db.Float, nullable=False)
        description = db.Column(db.Text)
        
        # العلاقات
        employees = db.relationship('Employee', backref='job_grade', lazy=True)

    class Employee(db.Model):
        """نموذج الموظفين"""
        id = db.Column(db.Integer, primary_key=True)
        employee_number = db.Column(db.String(50), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        national_id = db.Column(db.String(20), unique=True, nullable=False)
        birth_date = db.Column(db.Date, nullable=False)
        hire_date = db.Column(db.Date, nullable=False)
        phone = db.Column(db.String(20))
        email = db.Column(db.String(120))
        address = db.Column(db.Text)
        
        # المعلومات الوظيفية
        department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=False)
        job_grade_id = db.Column(db.Integer, db.ForeignKey('job_grade.id'), nullable=False)
        job_title = db.Column(db.String(200), nullable=False)
        current_salary = db.Column(db.Float, nullable=False)
        employment_status = db.Column(db.String(50), default='active')  # active, retired, terminated
        
        # معلومات إضافية
        marital_status = db.Column(db.String(20))
        gender = db.Column(db.String(10))
        education_level = db.Column(db.String(100))
        specialization = db.Column(db.String(200))
        
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        # العلاقات
        allowances = db.relationship('Allowance', backref='employee', lazy=True)
        promotions = db.relationship('Promotion', backref='employee', lazy=True)
        
        def __repr__(self):
            return f'<Employee {self.full_name}>'
        
        @property
        def age(self):
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        
        @property
        def years_of_service(self):
            today = date.today()
            return today.year - self.hire_date.year - ((today.month, today.day) < (self.hire_date.month, self.hire_date.day))

    class AllowanceType(db.Model):
        """نموذج أنواع العلاوات"""
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        code = db.Column(db.String(50), unique=True, nullable=False)
        description = db.Column(db.Text)
        amount_type = db.Column(db.String(20), nullable=False)  # fixed, percentage
        default_amount = db.Column(db.Float)
        is_active = db.Column(db.Boolean, default=True)
        
        # العلاقات
        allowances = db.relationship('Allowance', backref='allowance_type', lazy=True)

    class Allowance(db.Model):
        """نموذج العلاوات"""
        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
        allowance_type_id = db.Column(db.Integer, db.ForeignKey('allowance_type.id'), nullable=False)
        amount = db.Column(db.Float, nullable=False)
        date_granted = db.Column(db.Date, nullable=False)
        effective_date = db.Column(db.Date, nullable=False)
        reason = db.Column(db.Text)
        approved_by = db.Column(db.String(200))
        approval_date = db.Column(db.Date)
        status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
        notes = db.Column(db.Text)
        
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
        
        def __repr__(self):
            return f'<Allowance {self.employee.full_name} - {self.allowance_type.name}>'

    class Promotion(db.Model):
        """نموذج الترفيعات"""
        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
        from_grade_id = db.Column(db.Integer, db.ForeignKey('job_grade.id'), nullable=False)
        to_grade_id = db.Column(db.Integer, db.ForeignKey('job_grade.id'), nullable=False)
        promotion_date = db.Column(db.Date, nullable=False)
        effective_date = db.Column(db.Date, nullable=False)
        promotion_type = db.Column(db.String(50), nullable=False)  # regular, exceptional, merit
        reason = db.Column(db.Text)
        old_salary = db.Column(db.Float, nullable=False)
        new_salary = db.Column(db.Float, nullable=False)
        approved_by = db.Column(db.String(200))
        approval_date = db.Column(db.Date)
        status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
        notes = db.Column(db.Text)

        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

        # العلاقات
        from_grade = db.relationship('JobGrade', foreign_keys=[from_grade_id], backref='promotions_from')
        to_grade = db.relationship('JobGrade', foreign_keys=[to_grade_id], backref='promotions_to')

        def __repr__(self):
            return f'<Promotion {self.employee.full_name} - Grade {self.from_grade.grade_number} to {self.to_grade.grade_number}>'

    class EmployeeHistory(db.Model):
        """نموذج تاريخ الموظف"""
        id = db.Column(db.Integer, primary_key=True)
        employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
        action_type = db.Column(db.String(50), nullable=False)  # hire, promotion, allowance, transfer, etc.
        action_date = db.Column(db.Date, nullable=False)
        description = db.Column(db.Text, nullable=False)
        old_value = db.Column(db.Text)
        new_value = db.Column(db.Text)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

        # العلاقات
        employee = db.relationship('Employee', backref='history')

        def __repr__(self):
            return f'<EmployeeHistory {self.employee.full_name} - {self.action_type}>'

    return {
        'User': User,
        'Department': Department,
        'JobGrade': JobGrade,
        'Employee': Employee,
        'AllowanceType': AllowanceType,
        'Allowance': Allowance,
        'Promotion': Promotion,
        'EmployeeHistory': EmployeeHistory
    }
