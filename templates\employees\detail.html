{% extends "base.html" %}

{% block title %}{{ employee.full_name }} - تفاصيل الموظف{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-user"></i> {{ employee.full_name }}</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('employees') }}">الموظفين</a></li>
                        <li class="breadcrumb-item active">{{ employee.full_name }}</li>
                    </ol>
                </nav>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-warning">
                    <i class="fas fa-edit"></i> تعديل البيانات
                </a>
                <a href="{{ url_for('employees') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- المعلومات الأساسية -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                        <h5 class="mt-2">{{ employee.full_name }}</h5>
                        <p class="text-muted">{{ employee.job_title }}</p>
                    </div>
                    
                    <table class="table table-sm">
                        <tr>
                            <td><strong>رقم الموظف:</strong></td>
                            <td>{{ employee.employee_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>رقم الهوية:</strong></td>
                            <td>{{ employee.national_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الميلاد:</strong></td>
                            <td>{{ employee.birth_date.strftime('%Y-%m-%d') }}</td>
                        </tr>
                        <tr>
                            <td><strong>العمر:</strong></td>
                            <td>{{ employee.age }} سنة</td>
                        </tr>
                        <tr>
                            <td><strong>الجنس:</strong></td>
                            <td>
                                {% if employee.gender == 'male' %}ذكر
                                {% elif employee.gender == 'female' %}أنثى
                                {% else %}-{% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>الحالة الاجتماعية:</strong></td>
                            <td>
                                {% if employee.marital_status == 'single' %}أعزب
                                {% elif employee.marital_status == 'married' %}متزوج
                                {% elif employee.marital_status == 'divorced' %}مطلق
                                {% elif employee.marital_status == 'widowed' %}أرمل
                                {% else %}-{% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- معلومات الاتصال -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-phone"></i>
                        معلومات الاتصال
                    </h6>
                </div>
                <div class="card-body">
                    <p><i class="fas fa-phone text-primary"></i> {{ employee.phone or '-' }}</p>
                    <p><i class="fas fa-envelope text-primary"></i> {{ employee.email or '-' }}</p>
                    <p><i class="fas fa-map-marker-alt text-primary"></i> {{ employee.address or '-' }}</p>
                </div>
            </div>
        </div>
        
        <!-- المعلومات الوظيفية -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase"></i>
                        المعلومات الوظيفية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>القسم:</strong></td>
                                    <td>{{ employee.department.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>المنصب:</strong></td>
                                    <td>{{ employee.job_title }}</td>
                                </tr>
                                <tr>
                                    <td><strong>الدرجة الوظيفية:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ employee.job_grade.grade_name }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التوظيف:</strong></td>
                                    <td>{{ employee.hire_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>سنوات الخدمة:</strong></td>
                                    <td>{{ employee.years_of_service }} سنة</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>الراتب الحالي:</strong></td>
                                    <td class="text-success"><strong>{{ "{:,.0f}".format(employee.current_salary) }} د.ع</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>حالة التوظيف:</strong></td>
                                    <td>
                                        {% if employee.employment_status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif employee.employment_status == 'retired' %}
                                            <span class="badge bg-secondary">متقاعد</span>
                                        {% elif employee.employment_status == 'terminated' %}
                                            <span class="badge bg-danger">منتهي الخدمة</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>المستوى التعليمي:</strong></td>
                                    <td>{{ employee.education_level or '-' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>التخصص:</strong></td>
                                    <td>{{ employee.specialization or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- العلاوات -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-money-bill-wave"></i>
                        العلاوات ({{ employee.allowances|length }})
                    </h6>
                    <a href="{{ url_for('add_allowance') }}?employee_id={{ employee.id }}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i> إضافة علاوة
                    </a>
                </div>
                <div class="card-body">
                    {% if employee.allowances %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>نوع العلاوة</th>
                                        <th>المبلغ</th>
                                        <th>تاريخ المنح</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for allowance in employee.allowances[-5:] %}
                                    <tr>
                                        <td>{{ allowance.allowance_type.name }}</td>
                                        <td>{{ "{:,.0f}".format(allowance.amount) }} د.ع</td>
                                        <td>{{ allowance.date_granted.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if allowance.status == 'approved' %}
                                                <span class="badge bg-success">معتمدة</span>
                                            {% elif allowance.status == 'pending' %}
                                                <span class="badge bg-warning">معلقة</span>
                                            {% elif allowance.status == 'rejected' %}
                                                <span class="badge bg-danger">مرفوضة</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if employee.allowances|length > 5 %}
                        <div class="text-center">
                            <a href="{{ url_for('allowances') }}?search={{ employee.full_name }}" class="btn btn-sm btn-outline-primary">
                                عرض جميع العلاوات
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                            <p>لا توجد علاوات لهذا الموظف</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- الترفيعات -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-arrow-up"></i>
                        الترفيعات ({{ employee.promotions|length }})
                    </h6>
                    <a href="{{ url_for('add_promotion') }}?employee_id={{ employee.id }}" class="btn btn-sm btn-warning">
                        <i class="fas fa-plus"></i> إضافة ترفيع
                    </a>
                </div>
                <div class="card-body">
                    {% if employee.promotions %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>من درجة</th>
                                        <th>إلى درجة</th>
                                        <th>تاريخ الترفيع</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for promotion in employee.promotions[-5:] %}
                                    <tr>
                                        <td>{{ promotion.from_grade.grade_name }}</td>
                                        <td>{{ promotion.to_grade.grade_name }}</td>
                                        <td>{{ promotion.promotion_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if promotion.promotion_type == 'regular' %}عادي
                                            {% elif promotion.promotion_type == 'exceptional' %}استثنائي
                                            {% elif promotion.promotion_type == 'merit' %}استحقاق
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if promotion.status == 'approved' %}
                                                <span class="badge bg-success">معتمد</span>
                                            {% elif promotion.status == 'pending' %}
                                                <span class="badge bg-warning">معلق</span>
                                            {% elif promotion.status == 'rejected' %}
                                                <span class="badge bg-danger">مرفوض</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if employee.promotions|length > 5 %}
                        <div class="text-center">
                            <a href="{{ url_for('promotions') }}?search={{ employee.full_name }}" class="btn btn-sm btn-outline-primary">
                                عرض جميع الترفيعات
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-arrow-up fa-2x mb-2"></i>
                            <p>لا توجد ترفيعات لهذا الموظف</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
