from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///government_hr.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# استيراد النماذج
from models import create_models
models_dict = create_models(db)

# استخراج النماذج
User = models_dict['User']
Department = models_dict['Department']
JobGrade = models_dict['JobGrade']
Employee = models_dict['Employee']
AllowanceType = models_dict['AllowanceType']
Allowance = models_dict['Allowance']
Promotion = models_dict['Promotion']
EmployeeHistory = models_dict['EmployeeHistory']

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات سريعة
    total_employees = Employee.query.count()
    total_allowances = Allowance.query.count()
    total_promotions = Promotion.query.count()
    
    # العلاوات الحديثة
    recent_allowances = Allowance.query.order_by(Allowance.date_granted.desc()).limit(5).all()
    
    # الترفيعات الحديثة
    recent_promotions = Promotion.query.order_by(Promotion.promotion_date.desc()).limit(5).all()
    
    return render_template('dashboard.html', 
                         total_employees=total_employees,
                         total_allowances=total_allowances,
                         total_promotions=total_promotions,
                         recent_allowances=recent_allowances,
                         recent_promotions=recent_promotions)

# مسارات إدارة الموظفين
@app.route('/employees')
@login_required
def employees():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)

    query = Employee.query
    if search:
        query = query.filter(Employee.full_name.contains(search))

    employees = query.paginate(page=page, per_page=10, error_out=False)
    return render_template('employees/list.html', employees=employees, search=search)

@app.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if request.method == 'POST':
        try:
            employee = Employee(
                employee_number=request.form['employee_number'],
                full_name=request.form['full_name'],
                national_id=request.form['national_id'],
                birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date(),
                hire_date=datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date(),
                phone=request.form.get('phone'),
                email=request.form.get('email'),
                address=request.form.get('address'),
                department_id=request.form['department_id'],
                job_grade_id=request.form['job_grade_id'],
                job_title=request.form['job_title'],
                current_salary=float(request.form['current_salary']),
                marital_status=request.form.get('marital_status'),
                gender=request.form.get('gender'),
                education_level=request.form.get('education_level'),
                specialization=request.form.get('specialization')
            )

            db.session.add(employee)
            db.session.commit()
            flash('تم إضافة الموظف بنجاح', 'success')
            return redirect(url_for('employees'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء إضافة الموظف', 'error')

    departments = Department.query.all()
    job_grades = JobGrade.query.all()
    return render_template('employees/add.html', departments=departments, job_grades=job_grades)

@app.route('/employees/<int:id>')
@login_required
def employee_detail(id):
    employee = Employee.query.get_or_404(id)
    return render_template('employees/detail.html', employee=employee)

@app.route('/employees/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_employee(id):
    employee = Employee.query.get_or_404(id)

    if request.method == 'POST':
        try:
            employee.employee_number = request.form['employee_number']
            employee.full_name = request.form['full_name']
            employee.national_id = request.form['national_id']
            employee.birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date()
            employee.hire_date = datetime.strptime(request.form['hire_date'], '%Y-%m-%d').date()
            employee.phone = request.form.get('phone')
            employee.email = request.form.get('email')
            employee.address = request.form.get('address')
            employee.department_id = request.form['department_id']
            employee.job_grade_id = request.form['job_grade_id']
            employee.job_title = request.form['job_title']
            employee.current_salary = float(request.form['current_salary'])
            employee.marital_status = request.form.get('marital_status')
            employee.gender = request.form.get('gender')
            employee.education_level = request.form.get('education_level')
            employee.specialization = request.form.get('specialization')
            employee.updated_at = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث بيانات الموظف بنجاح', 'success')
            return redirect(url_for('employee_detail', id=id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء تحديث بيانات الموظف', 'error')

    departments = Department.query.all()
    job_grades = JobGrade.query.all()
    return render_template('employees/edit.html', employee=employee, departments=departments, job_grades=job_grades)

# مسارات مؤقتة للصفحات المفقودة
@app.route('/allowances')
@login_required
def allowances():
    return render_template('coming_soon.html', page_title='العلاوات')

@app.route('/allowances/add')
@login_required
def add_allowance():
    return render_template('coming_soon.html', page_title='إضافة علاوة')

@app.route('/allowance-types')
@login_required
def allowance_types():
    return render_template('coming_soon.html', page_title='أنواع العلاوات')

@app.route('/promotions')
@login_required
def promotions():
    return render_template('coming_soon.html', page_title='الترفيعات')

@app.route('/promotions/add')
@login_required
def add_promotion():
    return render_template('coming_soon.html', page_title='إضافة ترفيع')

@app.route('/reports')
@login_required
def reports():
    return render_template('coming_soon.html', page_title='التقارير')

@app.route('/reports/allowances')
@login_required
def allowance_reports():
    return render_template('coming_soon.html', page_title='تقارير العلاوات')

@app.route('/reports/promotions')
@login_required
def promotion_reports():
    return render_template('coming_soon.html', page_title='تقارير الترفيعات')

@app.route('/profile')
@login_required
def profile():
    return render_template('coming_soon.html', page_title='الملف الشخصي')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)

            # إنشاء أقسام افتراضية
            departments = [
                Department(name='قسم الموارد البشرية', code='HR', description='إدارة شؤون الموظفين'),
                Department(name='قسم المالية', code='FIN', description='إدارة الشؤون المالية'),
                Department(name='قسم تقنية المعلومات', code='IT', description='إدارة التقنية والأنظمة')
            ]

            for dept in departments:
                db.session.add(dept)

            # إنشاء درجات وظيفية افتراضية
            job_grades = [
                JobGrade(grade_number=1, grade_name='الدرجة الأولى', basic_salary=1500000),
                JobGrade(grade_number=2, grade_name='الدرجة الثانية', basic_salary=1200000),
                JobGrade(grade_number=3, grade_name='الدرجة الثالثة', basic_salary=1000000),
                JobGrade(grade_number=4, grade_name='الدرجة الرابعة', basic_salary=800000),
                JobGrade(grade_number=5, grade_name='الدرجة الخامسة', basic_salary=600000)
            ]

            for grade in job_grades:
                db.session.add(grade)

            db.session.commit()

    app.run(debug=True, host='0.0.0.0', port=5000)
